#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多格式输出生成器
生成EPUB、PDF、Word等多种格式的双语文档
"""

import os
import tempfile
import logging
from typing import List, Dict, Optional
from pathlib import Path
from ..generator.bilingual_generator import BilingualGenerator
from ..converter.format_converter import FormatConverter

logger = logging.getLogger(__name__)


class MultiFormatGenerator:
    """多格式输出生成器"""
    
    # 默认输出格式
    DEFAULT_OUTPUT_FORMATS = ['epub', 'pdf', 'docx']
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化多格式生成器
        
        Args:
            temp_dir: 临时目录路径
        """
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.bilingual_generator = BilingualGenerator()
        self.converter = FormatConverter(temp_dir)
        self.temp_files = []
    
    def generate_all_formats(self,
                           original_chapters: List[str],
                           translated_chapters: List[str],
                           output_dir: str,
                           base_filename: str,
                           original_format: str = 'txt',
                           target_formats: Optional[List[str]] = None) -> Dict[str, Dict[str, str]]:
        """
        生成多种格式的双语和单语文档

        Args:
            original_chapters: 原文章节列表
            translated_chapters: 译文章节列表
            output_dir: 输出目录
            base_filename: 基础文件名（不含扩展名）
            original_format: 原始文件格式
            target_formats: 目标格式列表，如果为None则使用默认格式

        Returns:
            Dict[str, Dict[str, str]]: 版本类型到格式文件路径映射的字典
            格式: {
                'bilingual': {'epub': 'path1', 'pdf': 'path2', 'docx': 'path3'},
                'translated': {'epub': 'path4', 'pdf': 'path5', 'docx': 'path6'}
            }
        """
        if target_formats is None:
            target_formats = self.DEFAULT_OUTPUT_FORMATS.copy()
        
        logger.info(f"开始生成多格式输出: {target_formats}")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        results = {
            'bilingual': {},
            'translated': {}
        }

        try:
            # 生成双语版本
            logger.info("生成双语版本文档...")
            bilingual_results = self._generate_version_formats(
                original_chapters, translated_chapters, output_dir,
                f"{base_filename}_bilingual", target_formats, version_type='bilingual'
            )
            results['bilingual'] = bilingual_results

            # 生成单语版本（纯翻译）
            logger.info("生成单语版本文档...")
            translated_results = self._generate_version_formats(
                [], translated_chapters, output_dir,
                f"{base_filename}_translated", target_formats, version_type='translated'
            )
            results['translated'] = translated_results

            total_files = len(bilingual_results) + len(translated_results)
            logger.info(f"多格式生成完成，成功生成 {total_files} 个文件")
            return results
            
        except Exception as e:
            logger.error(f"多格式生成失败: {str(e)}")
            self.cleanup()
            raise

    def _generate_version_formats(self,
                                original_chapters: List[str],
                                translated_chapters: List[str],
                                output_dir: str,
                                base_filename: str,
                                target_formats: List[str],
                                version_type: str) -> Dict[str, str]:
        """
        生成特定版本的多种格式文档

        Args:
            original_chapters: 原文章节列表（单语版本时为空列表）
            translated_chapters: 译文章节列表
            output_dir: 输出目录
            base_filename: 基础文件名
            target_formats: 目标格式列表
            version_type: 版本类型 ('bilingual' 或 'translated')

        Returns:
            Dict[str, str]: 格式到文件路径的映射
        """
        results = {}

        try:
            # 首先生成EPUB格式（作为中间格式）
            epub_path = self._generate_epub_version(
                original_chapters, translated_chapters,
                output_dir, base_filename, version_type
            )

            if 'epub' in target_formats:
                results['epub'] = epub_path

            # 基于EPUB生成其他格式
            for fmt in target_formats:
                if fmt != 'epub':
                    output_path = os.path.join(output_dir, f"{base_filename}.{fmt}")

                    # 尝试使用Calibre转换，如果失败则使用备用方法
                    success = False

                    if self.converter.calibre_available:
                        success = self.converter.convert_epub_to_format(epub_path, output_path, fmt)
                        if success:
                            logger.info(f"生成{fmt}格式成功 (Calibre): {output_path}")

                    # 如果Calibre转换失败或不可用，使用备用方法
                    if not success:
                        logger.info(f"尝试使用备用方法生成{fmt}格式...")
                        success = self._generate_format_without_calibre_version(
                            original_chapters, translated_chapters, output_path, fmt, version_type
                        )
                        if success:
                            logger.info(f"生成{fmt}格式成功 (备用方法): {output_path}")

                    if success:
                        results[fmt] = output_path
                    else:
                        logger.warning(f"生成{fmt}格式失败")

            # 如果EPUB不在目标格式中，将其添加到临时文件列表以便清理
            if 'epub' not in target_formats:
                self.temp_files.append(epub_path)

            logger.info(f"{version_type}版本生成完成，成功生成 {len(results)} 种格式")
            return results

        except Exception as e:
            logger.error(f"{version_type}版本生成失败: {str(e)}")
            raise

    def _generate_epub(self,
                      original_chapters: List[str],
                      translated_chapters: List[str],
                      output_dir: str,
                      base_filename: str) -> str:
        """
        生成EPUB格式的双语文档
        
        Args:
            original_chapters: 原文章节列表
            translated_chapters: 译文章节列表
            output_dir: 输出目录
            base_filename: 基础文件名
            
        Returns:
            str: 生成的EPUB文件路径
        """
        epub_path = os.path.join(output_dir, f"{base_filename}.epub")
        
        # 使用双语生成器创建EPUB
        self.bilingual_generator.generate_bilingual_epub(
            original_chapters, translated_chapters, epub_path
        )
        
        logger.info(f"EPUB生成成功: {epub_path}")
        return epub_path

    def _generate_epub_version(self,
                              original_chapters: List[str],
                              translated_chapters: List[str],
                              output_dir: str,
                              base_filename: str,
                              version_type: str) -> str:
        """
        生成指定版本的EPUB格式文档

        Args:
            original_chapters: 原文章节列表（单语版本时为空列表）
            translated_chapters: 译文章节列表
            output_dir: 输出目录
            base_filename: 基础文件名
            version_type: 版本类型 ('bilingual' 或 'translated')

        Returns:
            str: 生成的EPUB文件路径
        """
        epub_path = os.path.join(output_dir, f"{base_filename}.epub")

        if version_type == 'bilingual':
            # 使用双语生成器创建EPUB
            self.bilingual_generator.generate_bilingual_epub(
                original_chapters, translated_chapters, epub_path
            )
        else:  # translated
            # 生成纯翻译版本EPUB
            self.bilingual_generator.generate_translated_epub(
                translated_chapters, epub_path
            )

        logger.info(f"{version_type}版本EPUB生成成功: {epub_path}")
        return epub_path

    def generate_single_format(self,
                             original_chapters: List[str],
                             translated_chapters: List[str],
                             output_path: str,
                             target_format: str) -> bool:
        """
        生成单一格式的双语文档
        
        Args:
            original_chapters: 原文章节列表
            translated_chapters: 译文章节列表
            output_path: 输出文件路径
            target_format: 目标格式
            
        Returns:
            bool: 是否生成成功
        """
        try:
            output_dir = os.path.dirname(output_path)
            base_filename = Path(output_path).stem
            
            if target_format == 'epub':
                # 直接生成EPUB
                self._generate_epub(original_chapters, translated_chapters, 
                                  output_dir, base_filename)
                return True
            else:
                # 先尝试使用Calibre转换，如果失败则使用备用方法
                success = False

                if self.converter.calibre_available:
                    # 先生成EPUB，再转换为目标格式
                    temp_epub_path = os.path.join(self.temp_dir, f"{base_filename}_temp.epub")
                    self.temp_files.append(temp_epub_path)

                    self._generate_epub(original_chapters, translated_chapters,
                                      self.temp_dir, f"{base_filename}_temp")

                    success = self.converter.convert_epub_to_format(
                        temp_epub_path, output_path, target_format
                    )

                # 如果Calibre转换失败或不可用，使用备用方法
                if not success:
                    logger.info(f"尝试使用备用方法生成{target_format}格式...")
                    success = self._generate_format_without_calibre(
                        original_chapters, translated_chapters, output_path, target_format
                    )

                return success
                
        except Exception as e:
            logger.error(f"生成{target_format}格式失败: {str(e)}")
            return False
    
    def get_supported_output_formats(self) -> List[str]:
        """获取支持的输出格式列表"""
        return list(self.converter.SUPPORTED_OUTPUT_FORMATS)
    
    def is_format_supported(self, format_name: str) -> bool:
        """检查格式是否支持"""
        return format_name.lower() in self.converter.SUPPORTED_OUTPUT_FORMATS
    
    def estimate_file_sizes(self, content_size: int) -> Dict[str, str]:
        """
        估算不同格式的文件大小
        
        Args:
            content_size: 内容大小（字节）
            
        Returns:
            Dict[str, str]: 格式到估算大小的映射
        """
        # 基于经验的大小估算倍数
        size_multipliers = {
            'txt': 1.0,
            'epub': 1.2,
            'pdf': 2.5,
            'docx': 1.8,
            'mobi': 1.3,
            'azw3': 1.4
        }
        
        estimates = {}
        for fmt, multiplier in size_multipliers.items():
            estimated_bytes = int(content_size * multiplier)
            if estimated_bytes < 1024:
                estimates[fmt] = f"{estimated_bytes} B"
            elif estimated_bytes < 1024 * 1024:
                estimates[fmt] = f"{estimated_bytes / 1024:.1f} KB"
            else:
                estimates[fmt] = f"{estimated_bytes / (1024 * 1024):.1f} MB"
        
        return estimates

    def _generate_format_without_calibre(self,
                                       original_chapters: List[str],
                                       translated_chapters: List[str],
                                       output_path: str,
                                       target_format: str) -> bool:
        """
        不依赖Calibre生成格式的备用方法
        """
        return self._generate_format_without_calibre_version(
            original_chapters, translated_chapters, output_path, target_format, 'bilingual'
        )

    def _generate_format_without_calibre_version(self,
                                               original_chapters: List[str],
                                               translated_chapters: List[str],
                                               output_path: str,
                                               target_format: str,
                                               version_type: str) -> bool:
        """
        不依赖Calibre生成指定版本格式的备用方法
        """
        try:
            if target_format == 'txt':
                return self._generate_txt_format_version(original_chapters, translated_chapters, output_path, version_type)
            elif target_format == 'docx':
                return self._generate_docx_format_version(original_chapters, translated_chapters, output_path, version_type)
            elif target_format == 'pdf':
                return self._generate_pdf_format_version(original_chapters, translated_chapters, output_path, version_type)
            else:
                logger.warning(f"不支持的备用格式: {target_format}")
                return False
        except Exception as e:
            logger.error(f"备用格式生成失败 {target_format}: {str(e)}")
            return False

    def _generate_txt_format(self, original_chapters: List[str],
                           translated_chapters: List[str], output_path: str) -> bool:
        """生成TXT格式"""
        return self._generate_txt_format_version(original_chapters, translated_chapters, output_path, 'bilingual')

    def _generate_txt_format_version(self, original_chapters: List[str],
                                   translated_chapters: List[str], output_path: str, version_type: str) -> bool:
        """生成指定版本的TXT格式"""
        try:
            content = []

            if version_type == 'bilingual':
                for original, translated in zip(original_chapters, translated_chapters):
                    # 直接拼接原文和译文，不添加标签
                    content.append(f"{original}\n\n{translated}")
            else:  # translated
                content = translated_chapters

            with open(output_path, 'w', encoding='utf-8') as f:
                if version_type == 'bilingual':
                    f.write('\n\n---\n\n'.join(content))
                else:
                    f.write('\n\n'.join(content))

            return True
        except Exception as e:
            logger.error(f"生成TXT格式失败: {str(e)}")
            return False

    def _generate_docx_format(self, original_chapters: List[str],
                            translated_chapters: List[str], output_path: str) -> bool:
        """生成DOCX格式"""
        return self._generate_docx_format_version(original_chapters, translated_chapters, output_path, 'bilingual')

    def _generate_docx_format_version(self, original_chapters: List[str],
                                    translated_chapters: List[str], output_path: str, version_type: str) -> bool:
        """生成指定版本的DOCX格式"""
        try:
            from docx import Document

            doc = Document()

            if version_type == 'bilingual':
                # 直接添加双语内容，不添加标题和章节
                for i, (original, translated) in enumerate(zip(original_chapters, translated_chapters)):
                    # 添加原文
                    p_original = doc.add_paragraph()
                    p_original.add_run(original)

                    # 添加译文
                    p_translated = doc.add_paragraph()
                    run = p_translated.add_run(translated)
                    run.italic = True

                    # 段落间添加适当间距
                    if i < len(original_chapters) - 1:
                        doc.add_paragraph()  # 空行分隔
            else:  # translated
                # 只添加翻译内容
                for i, translated in enumerate(translated_chapters):
                    p_translated = doc.add_paragraph()
                    p_translated.add_run(translated)

                    # 段落间添加适当间距
                    if i < len(translated_chapters) - 1:
                        doc.add_paragraph()  # 空行分隔

            doc.save(output_path)
            return True

        except ImportError:
            logger.error("需要安装python-docx: pip install python-docx")
            return False
        except Exception as e:
            logger.error(f"生成DOCX格式失败: {str(e)}")
            return False

    def _generate_pdf_format(self, original_chapters: List[str],
                           translated_chapters: List[str], output_path: str) -> bool:
        """生成PDF格式"""
        return self._generate_pdf_format_version(original_chapters, translated_chapters, output_path, 'bilingual')

    def _generate_pdf_format_version(self, original_chapters: List[str],
                                   translated_chapters: List[str], output_path: str, version_type: str) -> bool:
        """生成指定版本的PDF格式"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            styles = getSampleStyleSheet()

            # 注册中文字体
            chinese_font_name = 'Helvetica'
            font_registered = False

            # 尝试多种中文字体
            font_candidates = [
                ('C:/Windows/Fonts/simsun.ttc', 'SimSun'),
                ('C:/Windows/Fonts/msyh.ttc', 'Microsoft YaHei'),
                ('C:/Windows/Fonts/simhei.ttf', 'SimHei'),
                ('C:/Windows/Fonts/simkai.ttf', 'KaiTi'),
            ]

            for font_path, font_name in font_candidates:
                if os.path.exists(font_path):
                    try:
                        # 使用不同的字体名称避免冲突
                        font_id = f'Chinese_{font_name.replace(" ", "")}'
                        pdfmetrics.registerFont(TTFont(font_id, font_path))
                        chinese_font_name = font_id
                        font_registered = True
                        logger.info(f"成功注册中文字体: {font_name}")
                        break
                    except Exception as e:
                        logger.warning(f"注册字体 {font_name} 失败: {str(e)}")
                        continue

            if not font_registered:
                logger.warning("未找到可用的中文字体，使用默认字体")
                chinese_font_name = 'Helvetica'

            # 创建样式
            base_font = chinese_font_name if font_registered else 'Helvetica'

            content_style = ParagraphStyle(
                'Content',
                parent=styles['Normal'],
                fontName=base_font,
                fontSize=12,
                spaceAfter=12
            )

            translated_style = ParagraphStyle(
                'Translated',
                parent=styles['Normal'],
                fontName=base_font,
                fontSize=12,
                spaceAfter=20,
                leftIndent=0
            )

            # 构建内容
            story = []

            if version_type == 'bilingual':
                # 直接添加双语内容，不添加标题和章节
                for i, (original, translated) in enumerate(zip(original_chapters, translated_chapters)):
                    # 添加原文
                    story.append(Paragraph(original, content_style))

                    # 添加译文
                    story.append(Paragraph(f"<i>{translated}</i>", translated_style))

                    # 段落间添加适当间距
                    if i < len(original_chapters) - 1:
                        story.append(Spacer(1, 20))
            else:  # translated
                # 只添加翻译内容
                for i, translated in enumerate(translated_chapters):
                    story.append(Paragraph(translated, content_style))

                    # 段落间添加适当间距
                    if i < len(translated_chapters) - 1:
                        story.append(Spacer(1, 20))

            # 生成PDF
            doc.build(story)
            return True

        except ImportError:
            logger.error("需要安装reportlab: pip install reportlab")
            return False
        except Exception as e:
            logger.error(f"生成PDF格式失败: {str(e)}")
            return False

    def cleanup(self):
        """清理临时文件"""
        if self.temp_files:
            self.converter.cleanup_temp_files(self.temp_files)
            self.temp_files.clear()
            logger.debug("已清理临时文件")
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup()


def create_multi_format_generator(temp_dir: Optional[str] = None) -> MultiFormatGenerator:
    """
    创建多格式生成器实例
    
    Args:
        temp_dir: 临时目录路径
        
    Returns:
        MultiFormatGenerator: 多格式生成器实例
    """
    return MultiFormatGenerator(temp_dir)


def get_format_info() -> Dict[str, Dict[str, str]]:
    """
    获取支持格式的详细信息
    
    Returns:
        Dict[str, Dict[str, str]]: 格式信息
    """
    return {
        'epub': {
            'name': 'EPUB',
            'description': '标准电子书格式，支持大多数阅读器',
            'extension': '.epub',
            'mime_type': 'application/epub+zip'
        },
        'pdf': {
            'name': 'PDF',
            'description': '便携式文档格式，保持固定布局',
            'extension': '.pdf',
            'mime_type': 'application/pdf'
        },
        'docx': {
            'name': 'Word文档',
            'description': 'Microsoft Word文档格式',
            'extension': '.docx',
            'mime_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        },
        'txt': {
            'name': '纯文本',
            'description': '简单的文本格式，兼容性最好',
            'extension': '.txt',
            'mime_type': 'text/plain'
        },
        'mobi': {
            'name': 'MOBI',
            'description': 'Amazon Kindle格式',
            'extension': '.mobi',
            'mime_type': 'application/x-mobipocket-ebook'
        }
    }
