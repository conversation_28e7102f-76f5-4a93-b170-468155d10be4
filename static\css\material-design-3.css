/* Material Design 3 - 核心樣式系統 */

/* ===== 色彩系統 (Material You) ===== */
:root {
  /* Primary Colors */
  --md-sys-color-primary: #6750A4;
  --md-sys-color-on-primary: #FFFFFF;
  --md-sys-color-primary-container: #EADDFF;
  --md-sys-color-on-primary-container: #21005D;

  /* Secondary Colors */
  --md-sys-color-secondary: #625B71;
  --md-sys-color-on-secondary: #FFFFFF;
  --md-sys-color-secondary-container: #E8DEF8;
  --md-sys-color-on-secondary-container: #1D192B;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #7D5260;
  --md-sys-color-on-tertiary: #FFFFFF;
  --md-sys-color-tertiary-container: #FFD8E4;
  --md-sys-color-on-tertiary-container: #31111D;

  /* Error Colors */
  --md-sys-color-error: #BA1A1A;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #FFDAD6;
  --md-sys-color-on-error-container: #410002;

  /* Surface Colors */
  --md-sys-color-surface: #FEF7FF;
  --md-sys-color-on-surface: #1C1B1F;
  --md-sys-color-surface-variant: #E7E0EC;
  --md-sys-color-on-surface-variant: #49454F;
  --md-sys-color-surface-container-lowest: #FFFFFF;
  --md-sys-color-surface-container-low: #F7F2FA;
  --md-sys-color-surface-container: #F3EDF7;
  --md-sys-color-surface-container-high: #ECE6F0;
  --md-sys-color-surface-container-highest: #E6E0E9;

  /* Outline Colors */
  --md-sys-color-outline: #79747E;
  --md-sys-color-outline-variant: #CAC4D0;

  /* Background Colors */
  --md-sys-color-background: #FEF7FF;
  --md-sys-color-on-background: #1C1B1F;

  /* Inverse Colors */
  --md-sys-color-inverse-surface: #313033;
  --md-sys-color-inverse-on-surface: #F4EFF4;
  --md-sys-color-inverse-primary: #D0BCFF;

  /* Shadow and Scrim */
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;

  /* 字體系統 */
  --md-sys-typescale-display-large-font: 'Roboto';
  --md-sys-typescale-display-large-size: 57px;
  --md-sys-typescale-display-large-weight: 400;
  --md-sys-typescale-display-large-line-height: 64px;

  --md-sys-typescale-display-medium-font: 'Roboto';
  --md-sys-typescale-display-medium-size: 45px;
  --md-sys-typescale-display-medium-weight: 400;
  --md-sys-typescale-display-medium-line-height: 52px;

  --md-sys-typescale-display-small-font: 'Roboto';
  --md-sys-typescale-display-small-size: 36px;
  --md-sys-typescale-display-small-weight: 400;
  --md-sys-typescale-display-small-line-height: 44px;

  --md-sys-typescale-headline-large-font: 'Roboto';
  --md-sys-typescale-headline-large-size: 32px;
  --md-sys-typescale-headline-large-weight: 400;
  --md-sys-typescale-headline-large-line-height: 40px;

  --md-sys-typescale-headline-medium-font: 'Roboto';
  --md-sys-typescale-headline-medium-size: 28px;
  --md-sys-typescale-headline-medium-weight: 400;
  --md-sys-typescale-headline-medium-line-height: 36px;

  --md-sys-typescale-headline-small-font: 'Roboto';
  --md-sys-typescale-headline-small-size: 24px;
  --md-sys-typescale-headline-small-weight: 400;
  --md-sys-typescale-headline-small-line-height: 32px;

  --md-sys-typescale-title-large-font: 'Roboto';
  --md-sys-typescale-title-large-size: 22px;
  --md-sys-typescale-title-large-weight: 400;
  --md-sys-typescale-title-large-line-height: 28px;

  --md-sys-typescale-title-medium-font: 'Roboto';
  --md-sys-typescale-title-medium-size: 16px;
  --md-sys-typescale-title-medium-weight: 500;
  --md-sys-typescale-title-medium-line-height: 24px;

  --md-sys-typescale-title-small-font: 'Roboto';
  --md-sys-typescale-title-small-size: 14px;
  --md-sys-typescale-title-small-weight: 500;
  --md-sys-typescale-title-small-line-height: 20px;

  --md-sys-typescale-body-large-font: 'Roboto';
  --md-sys-typescale-body-large-size: 16px;
  --md-sys-typescale-body-large-weight: 400;
  --md-sys-typescale-body-large-line-height: 24px;

  --md-sys-typescale-body-medium-font: 'Roboto';
  --md-sys-typescale-body-medium-size: 14px;
  --md-sys-typescale-body-medium-weight: 400;
  --md-sys-typescale-body-medium-line-height: 20px;

  --md-sys-typescale-body-small-font: 'Roboto';
  --md-sys-typescale-body-small-size: 12px;
  --md-sys-typescale-body-small-weight: 400;
  --md-sys-typescale-body-small-line-height: 16px;

  --md-sys-typescale-label-large-font: 'Roboto';
  --md-sys-typescale-label-large-size: 14px;
  --md-sys-typescale-label-large-weight: 500;
  --md-sys-typescale-label-large-line-height: 20px;

  --md-sys-typescale-label-medium-font: 'Roboto';
  --md-sys-typescale-label-medium-size: 12px;
  --md-sys-typescale-label-medium-weight: 500;
  --md-sys-typescale-label-medium-line-height: 16px;

  --md-sys-typescale-label-small-font: 'Roboto';
  --md-sys-typescale-label-small-size: 11px;
  --md-sys-typescale-label-small-weight: 500;
  --md-sys-typescale-label-small-line-height: 16px;

  /* 形狀系統 */
  --md-sys-shape-corner-none: 0px;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 50%;

  /* 陰影系統 */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

  /* 動畫系統 */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;

  --md-sys-motion-easing-linear: cubic-bezier(0, 0, 1, 1);
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
}

/* 暗色主題 */
[data-theme="dark"] {
  /* Primary Colors */
  --md-sys-color-primary: #D0BCFF;
  --md-sys-color-on-primary: #381E72;
  --md-sys-color-primary-container: #4F378B;
  --md-sys-color-on-primary-container: #EADDFF;

  /* Secondary Colors */
  --md-sys-color-secondary: #CCC2DC;
  --md-sys-color-on-secondary: #332D41;
  --md-sys-color-secondary-container: #4A4458;
  --md-sys-color-on-secondary-container: #E8DEF8;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #EFB8C8;
  --md-sys-color-on-tertiary: #492532;
  --md-sys-color-tertiary-container: #633B48;
  --md-sys-color-on-tertiary-container: #FFD8E4;

  /* Error Colors */
  --md-sys-color-error: #FFB4AB;
  --md-sys-color-on-error: #690005;
  --md-sys-color-error-container: #93000A;
  --md-sys-color-on-error-container: #FFDAD6;

  /* Surface Colors */
  --md-sys-color-surface: #10131C;
  --md-sys-color-on-surface: #E6E0E9;
  --md-sys-color-surface-variant: #49454F;
  --md-sys-color-on-surface-variant: #CAC4D0;
  --md-sys-color-surface-container-lowest: #0B0E17;
  --md-sys-color-surface-container-low: #1C1B1F;
  --md-sys-color-surface-container: #201F23;
  --md-sys-color-surface-container-high: #2B2930;
  --md-sys-color-surface-container-highest: #36343B;

  /* Outline Colors */
  --md-sys-color-outline: #938F99;
  --md-sys-color-outline-variant: #49454F;

  /* Background Colors */
  --md-sys-color-background: #10131C;
  --md-sys-color-on-background: #E6E0E9;

  /* Inverse Colors */
  --md-sys-color-inverse-surface: #E6E0E9;
  --md-sys-color-inverse-on-surface: #313033;
  --md-sys-color-inverse-primary: #6750A4;
}

/* 基礎重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 基礎字體設定 */
body {
  font-family: var(--md-sys-typescale-body-large-font), 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  color: var(--md-sys-color-on-background);
  background-color: var(--md-sys-color-background);
  transition: background-color var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard),
              color var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

/* 字體樣式類 */
.display-large {
  font-family: var(--md-sys-typescale-display-large-font);
  font-size: var(--md-sys-typescale-display-large-size);
  font-weight: var(--md-sys-typescale-display-large-weight);
  line-height: var(--md-sys-typescale-display-large-line-height);
}

.display-medium {
  font-family: var(--md-sys-typescale-display-medium-font);
  font-size: var(--md-sys-typescale-display-medium-size);
  font-weight: var(--md-sys-typescale-display-medium-weight);
  line-height: var(--md-sys-typescale-display-medium-line-height);
}

.display-small {
  font-family: var(--md-sys-typescale-display-small-font);
  font-size: var(--md-sys-typescale-display-small-size);
  font-weight: var(--md-sys-typescale-display-small-weight);
  line-height: var(--md-sys-typescale-display-small-line-height);
}

.headline-large {
  font-family: var(--md-sys-typescale-headline-large-font);
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  line-height: var(--md-sys-typescale-headline-large-line-height);
}

.headline-medium {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
}

.headline-small {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
}

.title-large {
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  line-height: var(--md-sys-typescale-title-large-line-height);
}

.title-medium {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: var(--md-sys-typescale-title-medium-weight);
  line-height: var(--md-sys-typescale-title-medium-line-height);
}

.title-small {
  font-family: var(--md-sys-typescale-title-small-font);
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: var(--md-sys-typescale-title-small-weight);
  line-height: var(--md-sys-typescale-title-small-line-height);
}

.body-large {
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
}

.body-medium {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: var(--md-sys-typescale-body-medium-weight);
  line-height: var(--md-sys-typescale-body-medium-line-height);
}

.body-small {
  font-family: var(--md-sys-typescale-body-small-font);
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: var(--md-sys-typescale-body-small-weight);
  line-height: var(--md-sys-typescale-body-small-line-height);
}

.label-large {
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
}

.label-medium {
  font-family: var(--md-sys-typescale-label-medium-font);
  font-size: var(--md-sys-typescale-label-medium-size);
  font-weight: var(--md-sys-typescale-label-medium-weight);
  line-height: var(--md-sys-typescale-label-medium-line-height);
}

.label-small {
  font-family: var(--md-sys-typescale-label-small-font);
  font-size: var(--md-sys-typescale-label-small-size);
  font-weight: var(--md-sys-typescale-label-small-weight);
  line-height: var(--md-sys-typescale-label-small-line-height);
}

/* ===== Material Design 3 組件樣式 ===== */

/* 按鈕組件 */
.md-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 64px;
  height: 40px;
  padding: 0 24px;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-family: var(--md-sys-typescale-label-large-font);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.md-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-button:hover::before {
  opacity: 0.08;
}

.md-button:focus::before {
  opacity: 0.12;
}

.md-button:active::before {
  opacity: 0.12;
}

.md-button:disabled {
  cursor: not-allowed;
  opacity: 0.38;
}

/* 填充按鈕 */
.md-button--filled {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-level0);
}

.md-button--filled:hover {
  box-shadow: var(--md-sys-elevation-level1);
}

.md-button--filled:focus {
  box-shadow: var(--md-sys-elevation-level1);
}

.md-button--filled:active {
  box-shadow: var(--md-sys-elevation-level0);
}

.md-button--filled:disabled {
  background-color: var(--md-sys-color-on-surface);
  color: var(--md-sys-color-surface);
  box-shadow: var(--md-sys-elevation-level0);
  opacity: 0.12;
}

/* 色調按鈕 */
.md-button--tonal {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
  box-shadow: var(--md-sys-elevation-level0);
}

.md-button--tonal:hover {
  box-shadow: var(--md-sys-elevation-level1);
}

.md-button--tonal:focus {
  box-shadow: var(--md-sys-elevation-level1);
}

.md-button--tonal:active {
  box-shadow: var(--md-sys-elevation-level0);
}

/* 輪廓按鈕 */
.md-button--outlined {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-button--outlined:disabled {
  color: var(--md-sys-color-on-surface);
  border-color: var(--md-sys-color-on-surface);
  opacity: 0.12;
}

/* 文字按鈕 */
.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  padding: 0 12px;
}

.md-button--text:disabled {
  color: var(--md-sys-color-on-surface);
  opacity: 0.38;
}

/* 卡片組件 */
.md-card {
  background-color: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-medium);
  box-shadow: var(--md-sys-elevation-level1);
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
  overflow: hidden;
}

.md-card--elevated {
  background-color: var(--md-sys-color-surface-container-low);
  box-shadow: var(--md-sys-elevation-level1);
}

.md-card--elevated:hover {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-card--filled {
  background-color: var(--md-sys-color-surface-container-highest);
  box-shadow: var(--md-sys-elevation-level0);
}

.md-card--outlined {
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
  box-shadow: var(--md-sys-elevation-level0);
}

/* 輸入框組件 */
.md-text-field {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.md-text-field__input {
  width: 100%;
  height: 56px;
  padding: 16px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-extra-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  outline: none;
}

.md-text-field__input:focus {
  border-color: var(--md-sys-color-primary);
  border-width: 2px;
  padding: 15px; /* 調整padding以補償border變化 */
}

.md-text-field__input:disabled {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface);
  opacity: 0.38;
  cursor: not-allowed;
}

.md-text-field__label {
  position: absolute;
  top: 16px;
  left: 16px;
  color: var(--md-sys-color-on-surface-variant);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  pointer-events: none;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  transform-origin: left top;
}

.md-text-field__input:focus + .md-text-field__label,
.md-text-field__input:not(:placeholder-shown) + .md-text-field__label {
  transform: translateY(-24px) scale(0.75);
  color: var(--md-sys-color-primary);
}

/* 填充樣式輸入框 */
.md-text-field--filled .md-text-field__input {
  background-color: var(--md-sys-color-surface-container-highest);
  border: none;
  border-bottom: 1px solid var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small) var(--md-sys-shape-corner-extra-small) 0 0;
}

.md-text-field--filled .md-text-field__input:focus {
  border-bottom: 2px solid var(--md-sys-color-primary);
  padding: 16px 16px 15px 16px;
}

/* 選擇框組件 */
.md-select {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.md-select__input {
  width: 100%;
  height: 56px;
  padding: 16px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-extra-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  outline: none;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.md-select__input:focus {
  border-color: var(--md-sys-color-primary);
  border-width: 2px;
  padding: 15px 39px 15px 15px;
}

/* 進度條組件 */
.md-progress {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: var(--md-sys-color-surface-container-highest);
  border-radius: var(--md-sys-shape-corner-full);
  overflow: hidden;
}

.md-progress__bar {
  height: 100%;
  background-color: var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-full);
  transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.md-progress--indeterminate .md-progress__bar {
  width: 30%;
  animation: md-progress-indeterminate 2s infinite linear;
}

@keyframes md-progress-indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* 狀態標籤組件 */
.md-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 24px;
  padding: 0 8px;
  border-radius: var(--md-sys-shape-corner-full);
  font-family: var(--md-sys-typescale-label-small-font);
  font-size: var(--md-sys-typescale-label-small-size);
  font-weight: var(--md-sys-typescale-label-small-weight);
  line-height: var(--md-sys-typescale-label-small-line-height);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.md-badge--success {
  background-color: #4CAF50;
  color: #FFFFFF;
}

.md-badge--warning {
  background-color: #FF9800;
  color: #FFFFFF;
}

.md-badge--error {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);
}

.md-badge--info {
  background-color: #2196F3;
  color: #FFFFFF;
}

.md-badge--pending {
  background-color: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

/* 模態框組件 */
.md-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.32);
  opacity: 0;
  visibility: hidden;
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.md-modal.show {
  opacity: 1;
  visibility: visible;
}

.md-modal__content {
  background-color: var(--md-sys-color-surface-container-high);
  border-radius: var(--md-sys-shape-corner-extra-large);
  box-shadow: var(--md-sys-elevation-level3);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  transform: scale(0.8);
  transition: transform var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized-decelerate);
}

.md-modal.show .md-modal__content {
  transform: scale(1);
}

.md-modal__header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.md-modal__title {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.md-modal__body {
  padding: 16px 24px;
}

.md-modal__footer {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.md-modal__close {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  border-radius: var(--md-sys-shape-corner-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--md-sys-color-on-surface-variant);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-modal__close:hover {
  background-color: var(--md-sys-color-on-surface);
  opacity: 0.08;
}

/* 導航欄組件 */
.md-top-app-bar {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 64px;
  padding: 0 16px;
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  box-shadow: var(--md-sys-elevation-level0);
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.md-top-app-bar--elevated {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-top-app-bar__title {
  flex: 1;
  font-family: var(--md-sys-typescale-title-large-font);
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  line-height: var(--md-sys-typescale-title-large-line-height);
  margin: 0;
}

.md-top-app-bar__actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
