#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雙語書籍翻譯服務 - 生產環境啟動文件
"""

import os
from app import app
from src.utils.task_queue import start_task_queue
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if __name__ == '__main__':
    # 启动后台任务队列
    start_task_queue()
    logger.info("后台任务队列已启动")
    
    # 获取端口号（Hugging Face Spaces 使用 7860 端口）
    port = int(os.environ.get('PORT', 7860))
    
    # 生产环境运行
    app.run(
        debug=False,
        host='0.0.0.0',
        port=port
    )
