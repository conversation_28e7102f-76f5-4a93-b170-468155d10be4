<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page-title">双语书籍翻译服务</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">

    <!-- Material Design 3 样式 -->
    <link rel="stylesheet" href="/static/css/app.css">

    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- 主题过渡动画 -->
    <style>
        .theme-transitioning * {
            transition: background-color 300ms cubic-bezier(0.2, 0, 0, 1),
                       color 300ms cubic-bezier(0.2, 0, 0, 1),
                       border-color 300ms cubic-bezier(0.2, 0, 0, 1) !important;
        }

        .theme-ripple {
            position: fixed;
            border-radius: 50%;
            background: var(--md-sys-color-primary);
            pointer-events: none;
            z-index: 9999;
            width: 0;
            height: 0;
            opacity: 0.3;
            transform: translate(-50%, -50%);
            transition: width 600ms cubic-bezier(0.2, 0, 0, 1),
                       height 600ms cubic-bezier(0.2, 0, 0, 1),
                       opacity 600ms cubic-bezier(0.2, 0, 0, 1);
        }

        .theme-ripple.active {
            width: 200vmax;
            height: 200vmax;
            opacity: 0;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: currentColor;
            opacity: 0.3;
            pointer-events: none;
            animation: ripple-animation 600ms ease-out;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header md-top-app-bar md-top-app-bar--elevated">
            <div class="header-content">
                <h1 class="md-top-app-bar__title">
                    <a href="/" class="app-title headline-medium" data-i18n="main-title">双语书籍翻译服务</a>
                </h1>

                <div class="md-top-app-bar__actions">
                    <!-- 用户面板 -->
                    <div class="user-panel">
                        <div class="user-info" id="userInfo">
                            <span class="body-medium" data-i18n="welcome">欢迎，</span>
                            <span id="username" class="body-medium"></span>
                            <div class="points-display">
                                <span class="material-icons">stars</span>
                                <span id="userPoints">0</span>
                            </div>
                            <button class="md-button md-button--tonal" onclick="dailySignIn()" id="signinBtn">
                                <span class="material-icons">event_available</span>
                                <span data-i18n="daily-signin">每日签到</span>
                            </button>
                            <button class="md-button md-button--text" onclick="showTaskModal()">
                                <span class="material-icons">assignment</span>
                                <span data-i18n="my-tasks">我的任务</span>
                            </button>
                            <button class="md-button md-button--outlined" onclick="logout()">
                                <span class="material-icons">logout</span>
                                <span data-i18n="logout">退出</span>
                            </button>
                        </div>
                        <div id="authButtons">
                            <button class="md-button md-button--filled" onclick="showAuthModal('login')">
                                <span class="material-icons">login</span>
                                <span data-i18n="login">登录</span>
                            </button>
                            <button class="md-button md-button--outlined" onclick="showAuthModal('register')">
                                <span class="material-icons">person_add</span>
                                <span data-i18n="register">注册</span>
                            </button>
                        </div>

                        <!-- 语言切换器 -->
                        <div class="language-switcher">
                            <button class="lang-btn active" data-lang="zh" id="lang-zh">中文</button>
                            <button class="lang-btn" data-lang="en" id="lang-en">English</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 英雄区域 -->
            <section class="hero-section">
                <h1 class="hero-title display-small" data-i18n="main-title">双语书籍翻译服务</h1>
                <p class="hero-subtitle body-large" data-i18n="main-subtitle">将您的书籍转换为双语版本，支持多种语言和翻译风格</p>
            </section>

            <!-- 翻译表单卡片 -->
            <div class="md-card md-card--elevated">
                <form id="uploadForm" class="upload-section">
                    <!-- 文件上传区域 -->
                    <div class="upload-area" id="uploadArea">
                        <span class="upload-icon material-icons">upload_file</span>
                        <h3 class="upload-title title-medium" data-i18n="upload-title">拖拽文件到此处或点击选择</h3>
                        <p class="upload-subtitle body-medium" data-i18n="upload-subtitle">支持 TXT、EPUB、PDF、Word、MOBI 等16种格式，最大 100MB</p>
                        <input type="file" id="fileInput" accept=".txt,.epub,.pdf,.docx,.doc,.mobi,.azw,.azw3,.fb2,.html,.htm,.rtf,.odt" style="display: none;">
                    </div>

                    <!-- 文件信息显示 -->
                    <div class="file-info" id="fileInfo">
                        <div class="file-info-item">
                            <span class="file-info-label body-medium" data-i18n="selected-file">已选择文件：</span>
                            <span class="file-info-value body-medium" id="fileName"></span>
                        </div>
                        <div class="file-info-item">
                            <span class="file-info-label body-medium" data-i18n="file-size">文件大小：</span>
                            <span class="file-info-value body-medium" id="fileSize"></span>
                        </div>
                    </div>

                    <!-- 翻译设置 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label label-large" for="targetLanguage" data-i18n="target-language">目标语言</label>
                            <div class="md-select">
                                <select id="targetLanguage" class="md-select__input">
                                    <option value="中文">中文</option>
                                    <option value="English">English</option>
                                    <option value="日本語">日本語</option>
                                    <option value="한국어">한국어</option>
                                    <option value="Français">Français</option>
                                    <option value="Deutsch">Deutsch</option>
                                    <option value="Español">Español</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label label-large" for="translationStyle" data-i18n="translation-style">翻译风格</label>
                            <div class="md-select">
                                <select id="translationStyle" class="md-select__input">
                                    <option value="casual" data-i18n="style-casual">口语化 - 轻松自然的表达</option>
                                    <option value="faithful" data-i18n="style-faithful">严谨忠实 - 保持原文结构</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 翻译按钮 -->
                    <button type="submit" class="md-button md-button--filled" id="translateBtn" style="width: 100%; height: 48px;">
                        <span class="material-icons">translate</span>
                        <span data-i18n="start-translation">开始翻译</span>
                    </button>
                </form>

                <!-- 进度显示 -->
                <div class="progress-section" id="progressContainer">
                    <div class="progress-label">
                        <span class="body-medium" data-i18n="translating">正在翻译中...</span>
                        <span class="progress-percentage body-medium">0%</span>
                    </div>
                    <div class="md-progress">
                        <div class="md-progress__bar" id="progressBar"></div>
                    </div>
                </div>

                <!-- 状态消息 -->
                <div class="status-message" id="statusMessage"></div>
            </div>

            <!-- 下载区域 -->
            <div class="download-section md-card md-card--filled">
                <h3 class="download-title title-medium" data-i18n="download-formats">下载格式选择：</h3>
                <div class="download-buttons" id="downloadButtons">
                    <!-- 下载按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </main>

        <!-- 认证模态框 -->
        <div id="authModal" class="md-modal">
            <div class="md-modal__content">
                <div class="md-modal__header">
                    <h2 class="md-modal__title" id="authModalTitle" data-i18n="login-title">用户登录</h2>
                    <button class="md-modal__close" onclick="closeAuthModal()">
                        <span class="material-icons">close</span>
                    </button>
                </div>

                <!-- 登录表单 -->
                <div id="loginForm" class="md-modal__body">
                    <form onsubmit="handleLogin(event)">
                        <div class="md-text-field">
                            <input type="email" id="loginUsername" class="md-text-field__input" placeholder=" " required>
                            <label class="md-text-field__label" data-i18n="email-placeholder">邮箱</label>
                        </div>
                        <div class="md-text-field" style="margin-top: 16px;">
                            <input type="password" id="loginPassword" class="md-text-field__input" placeholder=" " required>
                            <label class="md-text-field__label" data-i18n="password-placeholder">密码</label>
                        </div>
                        <div class="md-modal__footer">
                            <button type="submit" class="md-button md-button--filled" data-i18n="login">登录</button>
                        </div>
                    </form>
                    <div class="auth-switch body-medium" style="text-align: center; margin-top: 16px; color: var(--md-sys-color-on-surface-variant);">
                        <span data-i18n="no-account">还没有账号？</span>
                        <a onclick="switchToRegister()" style="color: var(--md-sys-color-primary); cursor: pointer;" data-i18n="register-now">立即注册</a>
                    </div>
                </div>

                <!-- 注册表单 -->
                <div id="registerForm" class="md-modal__body" style="display: none;">
                    <form onsubmit="handleRegister(event)">
                        <div class="md-text-field">
                            <input type="email" id="registerEmail" class="md-text-field__input" placeholder=" " required>
                            <label class="md-text-field__label" data-i18n="email-placeholder">邮箱</label>
                        </div>
                        <div class="md-text-field" style="margin-top: 16px;">
                            <input type="password" id="registerPassword" class="md-text-field__input" placeholder=" " required>
                            <label class="md-text-field__label" data-i18n="password-placeholder">密码</label>
                        </div>
                        <div class="md-text-field" style="margin-top: 16px;">
                            <input type="password" id="confirmPassword" class="md-text-field__input" placeholder=" " required>
                            <label class="md-text-field__label" data-i18n="confirm-password-placeholder">确认密码</label>
                        </div>
                        <div class="md-modal__footer">
                            <button type="submit" class="md-button md-button--filled" data-i18n="register">注册</button>
                        </div>
                    </form>
                    <div class="auth-switch body-medium" style="text-align: center; margin-top: 16px; color: var(--md-sys-color-on-surface-variant);">
                        <span data-i18n="have-account">已有账号？</span>
                        <a onclick="switchToLogin()" style="color: var(--md-sys-color-primary); cursor: pointer;" data-i18n="login-now">立即登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务管理模态框 -->
        <div id="taskModal" class="md-modal">
            <div class="md-modal__content" style="max-width: 900px;">
                <div class="md-modal__header">
                    <h2 class="md-modal__title" data-i18n="task-management">任务管理</h2>
                    <button class="md-modal__close" onclick="closeTaskModal()">
                        <span class="material-icons">close</span>
                    </button>
                </div>

                <div class="md-modal__body">
                    <div class="tab-buttons" style="display: flex; margin-bottom: 20px; border-bottom: 1px solid var(--md-sys-color-outline-variant);">
                        <button class="md-button md-button--text active" onclick="switchTab('tasks')" data-i18n="my-tasks">我的任务</button>
                        <button class="md-button md-button--text" onclick="switchTab('points')" data-i18n="points-history">积分历史</button>
                    </div>

                    <!-- 任务列表标签页 -->
                    <div id="tasksTab" class="tab-content active">
                        <div class="task-list" id="taskList">
                            <div data-i18n="loading">加载中...</div>
                        </div>
                    </div>

                    <!-- 积分历史标签页 -->
                    <div id="pointsTab" class="tab-content" style="display: none;">
                        <div class="points-history" id="pointsHistory">
                            <div data-i18n="loading">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- JavaScript 文件 -->
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/app.js"></script>
    <script src="/static/js/auth.js"></script>
</body>
</html>