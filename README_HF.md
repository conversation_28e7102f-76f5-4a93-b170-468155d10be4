---
title: Bilingual Book Translator
emoji: 📚
colorFrom: blue
colorTo: green
sdk: docker
pinned: false
license: mit
---

# 雙語書籍翻譯服務

這是一個基於Flask的雙語書籍翻譯服務，支持多種文件格式，使用Google Gemini API進行翻譯。

## 功能特點

- 支持多種文件格式：TXT、EPUB、PDF、DOCX等
- 使用Google Gemini API進行高質量翻譯
- 用戶認證和積分系統
- 多格式輸出：EPUB、PDF、Word
- 異步任務處理

## 使用方法

1. 註冊賬戶並獲得初始積分
2. 上傳您要翻譯的文件
3. 選擇目標語言和翻譯風格
4. 等待翻譯完成
5. 下載雙語版本文件

## 環境變量配置

在Space設置中需要配置以下環境變量：

- `GEMINI_API_KEY`: 您的Google Gemini API密鑰
- `SECRET_KEY`: 用於JWT令牌的密鑰

## 技術棧

- Flask + Python
- Google Gemini API
- Docker
