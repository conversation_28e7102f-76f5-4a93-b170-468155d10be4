/**
 * Material Design 3 主題系統
 * 支持明暗主題切換和動態色彩
 */

class ThemeManager {
  constructor() {
    this.currentTheme = this.getStoredTheme() || this.getSystemTheme();
    this.init();
  }

  init() {
    this.applyTheme(this.currentTheme);
    this.setupThemeToggle();
    this.setupSystemThemeListener();
    this.setupColorSchemeListener();
  }

  getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  getStoredTheme() {
    return localStorage.getItem('theme');
  }

  storeTheme(theme) {
    localStorage.setItem('theme', theme);
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.currentTheme = theme;
    this.storeTheme(theme);
    this.updateThemeToggleButton();
    this.dispatchThemeChangeEvent();
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);
  }

  setupThemeToggle() {
    // 創建主題切換按鈕
    const themeToggle = document.createElement('button');
    themeToggle.className = 'md-button md-button--text theme-toggle';
    themeToggle.setAttribute('aria-label', '切換主題');
    themeToggle.innerHTML = '<span class="material-icons">brightness_6</span>';
    
    // 添加到用戶面板
    const userPanel = document.querySelector('.user-panel');
    if (userPanel) {
      userPanel.appendChild(themeToggle);
    }

    themeToggle.addEventListener('click', () => {
      this.toggleTheme();
      this.animateThemeTransition();
    });
  }

  updateThemeToggleButton() {
    const themeToggle = document.querySelector('.theme-toggle');
    if (themeToggle) {
      const icon = themeToggle.querySelector('.material-icons');
      icon.textContent = this.currentTheme === 'light' ? 'dark_mode' : 'light_mode';
      themeToggle.setAttribute('aria-label', 
        this.currentTheme === 'light' ? '切換到暗色主題' : '切換到亮色主題'
      );
    }
  }

  setupSystemThemeListener() {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!this.getStoredTheme()) {
        this.applyTheme(e.matches ? 'dark' : 'light');
      }
    });
  }

  setupColorSchemeListener() {
    // 監聽系統色彩變化（如果支持）
    if ('matchMedia' in window) {
      const colorSchemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      colorSchemeQuery.addEventListener('change', () => {
        this.generateDynamicColors();
      });
    }
  }

  animateThemeTransition() {
    // 添加過渡動畫類
    document.documentElement.classList.add('theme-transitioning');
    
    // 創建波紋效果
    this.createThemeRipple();
    
    // 移除過渡類
    setTimeout(() => {
      document.documentElement.classList.remove('theme-transitioning');
    }, 300);
  }

  createThemeRipple() {
    const ripple = document.createElement('div');
    ripple.className = 'theme-ripple';
    
    // 設置波紋起始位置（主題按鈕位置）
    const themeToggle = document.querySelector('.theme-toggle');
    if (themeToggle) {
      const rect = themeToggle.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      ripple.style.left = centerX + 'px';
      ripple.style.top = centerY + 'px';
    }
    
    document.body.appendChild(ripple);
    
    // 觸發動畫
    requestAnimationFrame(() => {
      ripple.classList.add('active');
    });
    
    // 清理
    setTimeout(() => {
      ripple.remove();
    }, 600);
  }

  generateDynamicColors() {
    // 基於用戶偏好或圖片生成動態色彩
    // 這裡可以實現Material You的動態色彩功能
    console.log('Generating dynamic colors...');
  }

  dispatchThemeChangeEvent() {
    const event = new CustomEvent('themechange', {
      detail: { theme: this.currentTheme }
    });
    document.dispatchEvent(event);
  }
}

/**
 * 動畫工具類
 */
class AnimationUtils {
  static fadeIn(element, duration = 300) {
    element.style.opacity = '0';
    element.style.display = 'block';
    
    const animation = element.animate([
      { opacity: 0, transform: 'translateY(10px)' },
      { opacity: 1, transform: 'translateY(0)' }
    ], {
      duration,
      easing: 'cubic-bezier(0.2, 0, 0, 1)',
      fill: 'forwards'
    });
    
    animation.onfinish = () => {
      element.style.opacity = '';
      element.style.transform = '';
    };
    
    return animation;
  }

  static fadeOut(element, duration = 300) {
    const animation = element.animate([
      { opacity: 1, transform: 'translateY(0)' },
      { opacity: 0, transform: 'translateY(-10px)' }
    ], {
      duration,
      easing: 'cubic-bezier(0.2, 0, 0, 1)',
      fill: 'forwards'
    });
    
    animation.onfinish = () => {
      element.style.display = 'none';
      element.style.opacity = '';
      element.style.transform = '';
    };
    
    return animation;
  }

  static slideIn(element, direction = 'up', duration = 300) {
    const transforms = {
      up: 'translateY(100%)',
      down: 'translateY(-100%)',
      left: 'translateX(100%)',
      right: 'translateX(-100%)'
    };

    element.style.display = 'block';
    
    const animation = element.animate([
      { transform: transforms[direction], opacity: 0 },
      { transform: 'translate(0)', opacity: 1 }
    ], {
      duration,
      easing: 'cubic-bezier(0.2, 0, 0, 1)',
      fill: 'forwards'
    });
    
    return animation;
  }

  static createRipple(element, event) {
    const ripple = document.createElement('span');
    ripple.className = 'ripple';
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    
    element.appendChild(ripple);
    
    ripple.addEventListener('animationend', () => {
      ripple.remove();
    });
  }

  static staggerAnimation(elements, animationFn, delay = 100) {
    elements.forEach((element, index) => {
      setTimeout(() => {
        animationFn(element);
      }, index * delay);
    });
  }
}

/**
 * 響應式工具類
 */
class ResponsiveUtils {
  static getBreakpoint() {
    const width = window.innerWidth;
    if (width < 600) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  static isMobile() {
    return this.getBreakpoint() === 'mobile';
  }

  static isTablet() {
    return this.getBreakpoint() === 'tablet';
  }

  static isDesktop() {
    return this.getBreakpoint() === 'desktop';
  }

  static setupResponsiveListener(callback) {
    let currentBreakpoint = this.getBreakpoint();
    
    window.addEventListener('resize', () => {
      const newBreakpoint = this.getBreakpoint();
      if (newBreakpoint !== currentBreakpoint) {
        currentBreakpoint = newBreakpoint;
        callback(newBreakpoint);
      }
    });
  }
}

// 初始化主題管理器
let themeManager;

document.addEventListener('DOMContentLoaded', () => {
  themeManager = new ThemeManager();
  
  // 設置響應式監聽器
  ResponsiveUtils.setupResponsiveListener((breakpoint) => {
    console.log('Breakpoint changed:', breakpoint);
    // 可以在這裡處理響應式變化
  });
  
  // 為所有按鈕添加波紋效果
  document.addEventListener('click', (e) => {
    if (e.target.matches('.md-button, .md-button *')) {
      const button = e.target.closest('.md-button');
      if (button && !button.disabled) {
        AnimationUtils.createRipple(button, e);
      }
    }
  });
});

// 導出工具類供其他模塊使用
window.ThemeManager = ThemeManager;
window.AnimationUtils = AnimationUtils;
window.ResponsiveUtils = ResponsiveUtils;
