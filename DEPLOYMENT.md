# 雙語書籍翻譯服務 - 部署指南

## 項目概述

這是一個基於Flask的雙語書籍翻譯服務，支持多種文件格式（TXT、EPUB、PDF、DOCX等），使用Gemini API進行翻譯，並提供用戶認證和積分系統。

## 部署架構

- **前端**: Cloudflare Pages (靜態文件託管)
- **後端**: Hugging Face Spaces (Docker容器)
- **API**: Google Gemini API

## 準備工作

### 1. 獲取API密鑰

1. 訪問 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 創建新的API密鑰
3. 保存API密鑰，稍後需要配置

### 2. 準備代碼

確保您的項目目錄包含以下文件：
```
bilingual_read/
├── app.py              # 主應用程序
├── main.py             # 生產環境啟動文件
├── Dockerfile          # Docker配置
├── requirements.txt    # Python依賴
├── src/               # 源代碼目錄
├── templates/         # HTML模板
└── static/           # 靜態文件（如果有）
```

## 後端部署 (Hugging Face Spaces)

### 步驟1: 創建Hugging Face賬戶

1. 訪問 [Hugging Face](https://huggingface.co/)
2. 註冊或登錄賬戶

### 步驟2: 創建新的Space

1. 點擊右上角的 "+" 按鈕
2. 選擇 "Create new Space"
3. 填寫以下信息：
   - **Space name**: `bilingual-translator` (或您喜歡的名稱)
   - **License**: Apache 2.0
   - **Select the SDK**: Docker
   - **Hardware**: CPU basic (免費) 或 CPU upgrade (付費，性能更好)

### 步驟3: 配置環境變量

在Space設置中添加以下環境變量：

1. 點擊Space頁面的 "Settings" 標籤
2. 在 "Variables and secrets" 部分添加：
   - `GEMINI_API_KEY`: 您的Google Gemini API密鑰
   - `SECRET_KEY`: 隨機生成的密鑰（用於JWT令牌）

生成SECRET_KEY的方法：
```python
import secrets
print(secrets.token_hex(32))
```

### 步驟4: 上傳代碼

有兩種方式上傳代碼：

#### 方式A: 使用Git (推薦)

1. 克隆您的Space倉庫：
```bash
git clone https://huggingface.co/spaces/YOUR_USERNAME/bilingual-translator
cd bilingual-translator
```

2. 複製您的項目文件到這個目錄

3. 提交並推送：
```bash
git add .
git commit -m "Initial deployment"
git push
```

#### 方式B: 使用Web界面

1. 在Space頁面點擊 "Files" 標籤
2. 點擊 "Add file" 上傳您的文件
3. 確保上傳所有必要的文件

### 步驟5: 等待部署

上傳完成後，Hugging Face會自動構建Docker鏡像並部署您的應用。您可以在 "Logs" 標籤中查看構建進度。

## 前端部署 (Cloudflare Pages)

### 步驟1: 準備前端文件

創建一個新的目錄用於前端：
```
frontend/
├── index.html
├── style.css
├── script.js
└── _redirects
```

### 步驟2: 修改前端配置

在前端JavaScript中，將API端點指向您的Hugging Face Space：
```javascript
const API_BASE_URL = 'https://YOUR_USERNAME-bilingual-translator.hf.space';
```

### 步驟3: 創建Cloudflare Pages

1. 登錄 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 選擇 "Pages" > "Create a project"
3. 選擇 "Upload assets" (如果沒有Git倉庫) 或連接您的Git倉庫
4. 上傳前端文件
5. 配置構建設置（如果需要）

### 步驟4: 配置CORS

確保後端允許來自Cloudflare Pages的請求。在您的Flask應用中已經配置了CORS。

## 配置說明

### 環境變量

| 變量名 | 描述 | 必需 |
|--------|------|------|
| `GEMINI_API_KEY` | Google Gemini API密鑰 | 是 |
| `SECRET_KEY` | JWT令牌密鑰 | 是 |
| `PORT` | 服務端口 (默認7860) | 否 |

### Gemini API配置

在 `src/translator/gemini_translator.py` 中，API密鑰通過環境變量獲取：
```python
self.api_key = os.environ.get('GEMINI_API_KEY')
```

## 測試部署

### 1. 檢查後端

訪問您的Hugging Face Space URL，應該能看到應用首頁。

### 2. 測試API

使用curl或Postman測試API端點：
```bash
curl https://YOUR_USERNAME-bilingual-translator.hf.space/api/upload
```

### 3. 測試完整流程

1. 註冊新用戶
2. 上傳測試文件
3. 開始翻譯任務
4. 檢查任務狀態
5. 下載翻譯結果

## 故障排除

### 常見問題

1. **API密鑰錯誤**
   - 檢查環境變量是否正確設置
   - 確認API密鑰有效且有足夠配額

2. **文件上傳失敗**
   - 檢查文件大小限制 (100MB)
   - 確認文件格式支持

3. **翻譯任務卡住**
   - 檢查後台任務隊列是否正常運行
   - 查看應用日誌

### 查看日誌

在Hugging Face Space中：
1. 點擊 "Logs" 標籤
2. 查看實時日誌輸出

## 擴展和優化

### 性能優化

1. **升級硬件**: 在Hugging Face中選擇更高性能的硬件
2. **緩存**: 實現翻譯結果緩存
3. **異步處理**: 優化後台任務隊列

### 功能擴展

1. **多語言支持**: 添加更多目標語言
2. **文件格式**: 支持更多文件格式
3. **用戶管理**: 添加管理員界面

## 安全注意事項

1. **API密鑰安全**: 永遠不要在代碼中硬編碼API密鑰
2. **用戶認證**: 確保JWT令牌安全
3. **文件上傳**: 驗證上傳文件的安全性
4. **速率限制**: 實現API調用速率限制

## 支持

如果遇到問題，請檢查：
1. Hugging Face Space日誌
2. Cloudflare Pages部署日誌
3. 瀏覽器開發者工具控制台

## 更新部署

要更新應用：
1. 修改代碼
2. 推送到Git倉庫（如果使用Git）
3. Hugging Face會自動重新部署

## 部署前檢查清單

在部署之前，請確保：

- [ ] 已獲得Google Gemini API密鑰
- [ ] 已生成SECRET_KEY
- [ ] 項目文件已清理（無測試文件）
- [ ] 所有依賴都在requirements.txt中
- [ ] Dockerfile配置正確
- [ ] 環境變量已準備好

## 文件清單

部署時應包含以下文件：
```
bilingual_read/
├── app.py              # 主應用程序
├── main.py             # 生產環境啟動文件
├── Dockerfile          # Docker配置
├── .dockerignore       # Docker忽略文件
├── requirements.txt    # Python依賴
├── README.md           # 項目說明
├── README_HF.md        # Hugging Face Space配置
├── DEPLOYMENT.md       # 部署指南
├── src/               # 源代碼目錄
├── templates/         # HTML模板
├── data/              # 數據目錄（空）
├── uploads/           # 上傳目錄（空）
└── outputs/           # 輸出目錄（空）
```

---

**注意**: 這是一個基本的部署指南。根據您的具體需求，可能需要進行額外的配置和優化。
