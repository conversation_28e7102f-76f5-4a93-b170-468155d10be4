#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模擬翻譯器 - 用於測試和演示
"""

import time
import random
from .base_translator import BaseTranslator


class MockTranslator(BaseTranslator):
    """模擬翻譯器，用於測試和演示"""
    
    def __init__(self):
        super().__init__()
        self.name = "Mock Translator"
        self.supported_languages = [
            "中文", "English", "日本語", "한국어", 
            "Français", "Deutsch", "Español"
        ]
    
    def translate_text(self, text, target_language="中文", style="casual"):
        """
        模擬翻譯文本
        
        Args:
            text (str): 要翻譯的文本
            target_language (str): 目標語言
            style (str): 翻譯風格 ('casual' 或 'faithful')
            
        Returns:
            str: 翻譯後的文本
        """
        # 模擬翻譯延遲
        time.sleep(random.uniform(0.1, 0.3))
        
        # 根據目標語言返回模擬翻譯
        if target_language == "中文":
            return f"[中文翻譯] {text}"
        elif target_language == "English":
            return f"[English Translation] {text}"
        elif target_language == "日本語":
            return f"[日本語翻訳] {text}"
        elif target_language == "한국어":
            return f"[한국어 번역] {text}"
        elif target_language == "Français":
            return f"[Traduction française] {text}"
        elif target_language == "Deutsch":
            return f"[Deutsche Übersetzung] {text}"
        elif target_language == "Español":
            return f"[Traducción española] {text}"
        else:
            return f"[Translation to {target_language}] {text}"
    
    def translate_batch(self, texts, target_language="中文", style="casual"):
        """
        批量翻譯文本
        
        Args:
            texts (list): 要翻譯的文本列表
            target_language (str): 目標語言
            style (str): 翻譯風格
            
        Returns:
            list: 翻譯後的文本列表
        """
        return [self.translate_text(text, target_language, style) for text in texts]
    
    def is_language_supported(self, language):
        """
        檢查是否支持指定語言
        
        Args:
            language (str): 語言名稱
            
        Returns:
            bool: 是否支持
        """
        return language in self.supported_languages
    
    def get_supported_languages(self):
        """
        獲取支持的語言列表
        
        Returns:
            list: 支持的語言列表
        """
        return self.supported_languages.copy()
    
    def estimate_cost(self, text_length):
        """
        估算翻譯成本（模擬）
        
        Args:
            text_length (int): 文本長度
            
        Returns:
            int: 估算的積分成本
        """
        # 模擬成本計算：每1000字符需要10積分
        return max(10, (text_length // 1000) * 10)
    
    def validate_input(self, text, target_language):
        """
        驗證輸入參數
        
        Args:
            text (str): 要翻譯的文本
            target_language (str): 目標語言
            
        Returns:
            tuple: (是否有效, 錯誤消息)
        """
        if not text or not text.strip():
            return False, "文本不能為空"
        
        if not self.is_language_supported(target_language):
            return False, f"不支持的語言: {target_language}"
        
        if len(text) > 50000:  # 限制最大長度
            return False, "文本太長，最大支持50000字符"
        
        return True, None
