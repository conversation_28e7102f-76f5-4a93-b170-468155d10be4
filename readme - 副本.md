# 雙語書籍翻譯服務網站 – 產品需求文檔（PRD）

## 📋 开发状态总览

### ✅ 已完成功能 (MVP v1.0)
- [x] **项目基础架构**
  - [x] Flask应用框架搭建
  - [x] 模块化代码结构
  - [x] 基础配置管理

- [x] **翻译引擎核心**
  - [x] Gemini API集成 (硬编码配置)
  - [x] 双翻译风格支持 (casual/faithful)
  - [x] 多语言目标支持
  - [x] 智能文本过滤

- [x] **文件处理模块**
  - [x] TXT文件加载器
  - [x] EPUB文件加载器 (基础版)
  - [x] 双语文档生成
  - [x] 文件格式验证

- [x] **Web界面**
  - [x] 响应式设计
  - [x] 多语言界面 (中文/英文)
  - [x] 文件拖拽上传
  - [x] 翻译进度显示

- [x] **API接口**
  - [x] 文件上传接口
  - [x] 翻译任务接口
  - [x] 任务状态查询
  - [x] 文件下载接口

- [x] **数据模型**
  - [x] 翻译任务模型
  - [x] 用户模型 (基础结构)
  - [x] 积分系统模型 (基础结构)

### 🔄 部分完成功能
- [x] **积分系统** (已实现模型，待集成业务逻辑)

### ⏳ 下一阶段开发计划

#### 🎯 第一阶段 - 用户系统 (预计2周)
- [ ] **用户认证系统**
  - [ ] 用户注册/登录功能
  - [ ] JWT令牌管理
  - [ ] 密码重置功能

- [ ] **积分系统集成**
  - [ ] 翻译扣除积分逻辑
  - [ ] 积分余额显示
  - [ ] 每日签到功能

#### 🎯 第二阶段 - 功能完善 (预计2周)
- [ ] **文件处理增强**
  - [ ] EPUB处理优化
  - [ ] PDF/DOCX文件支持
  - [ ] 批量文件处理

- [ ] **用户体验提升**
  - [ ] 翻译历史记录
  - [ ] 文件管理界面
  - [ ] 搜索和筛选功能

#### 🎯 第三阶段 - 商业化功能 (预计1周)
- [ ] **支付系统**
  - [ ] Stripe/支付宝集成
  - [ ] 积分购买功能
  - [ ] 订阅系统

#### 🎯 第四阶段 - 高级功能 (预计1周)
- [ ] **管理后台**
  - [ ] 用户管理界面
  - [ ] 翻译任务监控
  - [ ] 系统统计报表

> 详细开发计划请查看 `开发计划.md` 文件

## 快速开始

1. 安装依赖：`pip install -r requirements.txt`
2. 配置环境变量：编辑 `.env` 文件，填入你的 Gemini API 密钥
3. 运行应用：`python run.py`
4. 访问：`http://localhost:5000`

详细说明请参考 `README_MVP.md`

## 1. 項目背景與目標

* **背景**：閱讀外語書籍對許多讀者而言門檻較高，而市面上缺乏支援全語種、支持自助上傳 + 配分制的雙語翻譯網站。
* **核心目標**：

  1. 讓用戶可將任意語言的文本（epub、txt、docx、pdf 等）轉換為雙語版並下載多種格式。
  2. 提供積分機制（每日簽到贈送、付費購買），按字數/章節扣分。
  3. 支援多語種翻譯、翻譯風格（口語化 vs 嚴謹忠實）。
  4. 全站可部署於 Cloudflare 免費層（Pages + Workers + R2/KV/Durable Objects），移動端友好。

## 2. 目標用戶/Persona

| Persona | 目標       | 痛點            | 典型場景                  |
| ------- | -------- | ------------- | --------------------- |
| 留學生 A   | 快速閱讀外語教材 | 正版翻譯少，PDF 碎片化 | 上傳英文電子書→ 翻譯成中英對照 EPUB |
| 語言愛好者 B | 雙語對照學習   | 市面上雙語版本有限     | 上傳日文小說→ 設定口語化風格       |
| 出版工作者 C | 審校草稿     | AI 翻譯需嚴謹      | 上傳阿語手稿→ 嚴謹風格→ 雙語 PDF  |

## 3. 用戶故事 (User Stories)

1. **US01 註冊**：作為新用戶，我希望註冊帳號並獲得新手積分，以體驗翻譯功能。
2. **US02 上傳 & 設定**：作為用戶，我希望上傳文本並選擇目標語言與風格，以便獲得我想要的翻譯格式。
3. **US03 積分扣除**：作為用戶，我希望在提交翻譯時看到所需積分並確認扣除。
4. **US04 異步翻譯**：作為用戶，我希望關閉頁面後翻譯仍能完成，並在完成後收到通知。
5. **US05 下載結果**：作為用戶，我希望下載雙語/單語版本的 EPUB、PDF 等格式。
6. **US06 每日簽到**：作為用戶，我想每日簽到領取免費積分。
7. **US07 付費購分**：作為用戶，我想快速購買積分以翻譯大部頭書籍。

## 4. 功能需求 (FRD)

### 4.1 帳號與授權

* Email/第三方 OAuth 註冊、登入、忘記密碼
* JWT + Cloudflare Access Session

### 4.2 積分系統

* 初始贈送、每日簽到、Stripe／支付寶購買
* 交易記錄、餘額查詢
* 翻譯按「源語言字數 × 係數」扣分

### 4.3 文本上傳與格式處理

* 支援 txt、epub、docx、pdf（前端直接上傳 → R2）
* **自動轉 EPUB**：使用 calibre/epub-gen WebAssembly or serverless binary
* 文件大小上限： 100 MB

### 4.4 翻譯配置

* 目標語言：IETF BCP‑47 列表
* 風格：

  * `casual` → Prompt: "請用口語化方式翻譯以下內容…"
  * `faithful` → Prompt: "請忠實於原文，不增刪不潤色…"
* 其他附加選項：是否包含註釋、是否按段落對齊

### 4.5 翻譯排隊 & 異步任務

* Durable Object/Queue：將任務狀態標記為 `pending` → `processing` → `done` → `error`
* Worker 定時器或後端 Cron / RabbitMQ 取任務
* 完成後發送郵件 & Web 推送 (optional)

### 4.6 結果生成與下載

* PDF 生成：利用 pdf-lib / Headless Chrome
* 壓縮包格式：雙語 EPUB、雙語 PDF、單語 EPUB (目標/源)
* 下載有效期 7 天，過期自動清理 R2

### 4.7 首頁內容

* 精選公版書：展示封面、簡介、語言對照下載
* CTA：立即上傳翻譯

### 4.8 管理後台

* 儀表板：用戶列表、積分流水、翻譯任務狀態
* 內容審核：黑名單詞彙、違法上傳

## 5. 非功能需求 (NFR)

| 編號     | 類別    | 說明                             |
| ------ | ----- | ------------------------------ |
| NFR‑01 | 響應式   | 支援桌面、手機 (≤ 1 s FCP)            |
| NFR‑02 | 可擴展   | 水平擴充 Worker → 任務隊列服務器          |
| NFR‑03 | 安全    | HTTPS、CSP、XSS/CSRF 防護、DOS 防御   |
| NFR‑04 | 隱私與版權 | 僅允許用戶自有或公版文本，存儲加密              |
| NFR‑05 | 成本    | 優先使用 Cloudflare 免費層 (≤ \$50/月) |

## 6. 技術選型

* **前端**：

  * Cloudflare Pages
  * React + Vite + Tailwind (或 SvelteKit)
  * SWR for API、React‑Hook‑Form
* **後端 (API)**：

  * Cloudflare Workers (Hono 或 itty-router)
  * KV：Session/積分餘額
  * R2：文件存儲
  * Durable Objects / Queue：翻譯任務管理
* **備用後端**：Node + Express 部署在自有 VPS，跑翻譯 CLI
* **支付**：Stripe Checkout、Alipay Global SDK

## 7. 資料模型 (簡化 ERD)

```mermaid
erDiagram
    users ||--o{ points_transactions : has
    users ||--o{ uploads : owns
    uploads ||--o{ translation_jobs : generates
    translation_jobs ||--o{ file_assets : output

    users {
      string id PK
      string email
      string password_hash
      int    points_balance
      datetime created_at
    }
    points_transactions {
      string id PK
      string user_id FK
      int    delta
      string type "signup | signin | purchase | deduction"
      datetime created_at
    }
    uploads {
      string id PK
      string user_id FK
      string original_filename
      string source_lang
      int    word_count
      datetime created_at
    }
    translation_jobs {
      string id PK
      string upload_id FK
      string target_lang
      string style
      string status "pending | processing | done | error"
      int    cost_points
      datetime started_at
      datetime finished_at
    }
    file_assets {
      string id PK
      string job_id FK
      string format "epub | pdf"
      string variant "bilingual | target | source"
      string r2_key
    }
```

## 8. API Endpoints (REST)

| Method | Path                        | Auth | 描述                                   |
| ------ | --------------------------- | ---- | ------------------------------------ |
| POST   | /api/register               | ✗    | 註冊帳號                                 |
| POST   | /api/login                  | ✗    | 登入取得 JWT                             |
| POST   | /api/logout                 | ✓    | 退出                                   |
| GET    | /api/profile                | ✓    | 用戶資訊 + 積分餘額                          |
| POST   | /api/signin                 | ✓    | 每日簽到 (獲取積分)                          |
| POST   | /api/upload                 | ✓    | 上傳檔案 (multipart)                     |
| POST   | /api/translate              | ✓    | 提交翻譯任務（參數：uploadId、targetLang、style） |
| GET    | /api/job/\:id               | ✓    | 查詢任務狀態                               |
| GET    | /api/download/\:id/\:format | ✓    | 下載結果                                 |
| POST   | /api/purchase/checkout      | ✓    | 建立 Stripe Session                    |
| POST   | /api/purchase/webhook       | ✗    | Stripe Webhook                       |

### 8.1 狀態碼範例

* 202：翻譯任務排隊成功
* 403：積分不足
* 409：每日簽到已完成

## 9. 積分規則

| 項目   | 積分                          | 備註           |
| ---- | --------------------------- | ------------ |
| 註冊贈送 | 100                         | 一次性          |
| 每日簽到 | +10                         | 00:00 UTC 重置 |
| 字數翻譯 | -`ceil(word_count/100) * 2` | 每百詞 2 分      |
| 付費購分 | 1 USD ≈ 100 分               | 可配置          |

## 10. 支付流程

1. 前端呼叫 `/api/purchase/checkout` 帶入欲購買分數。
2. Worker 產生 Stripe Checkout Session → 回傳 sessionId。
3. 前端重導至 Stripe 託管頁。
4. 支付成功 → Stripe Webhook `/api/purchase/webhook`。
5. Worker 驗簽 & 更新 points\_transactions 與 users.points\_balance。

## 11. 翻譯提示詞範例

```text
// casual
你是一位友好的譯者，請將以下 {sourceLang} 內容翻譯成 {targetLang}，用輕鬆口語的筆調，確保意思準確但可以自然潤色。

// faithful
你是一位嚴謹的學術譯者，請將以下 {sourceLang} 內容翻譯成 {targetLang}，要求逐句對齊，盡量保留原句結構與術語。
```

## 12. 邊界情況 & 風險

* 版權爭議：引導聲明僅供學習，禁止上傳未授權受限作品。
* 大檔案超時：分片上傳 + 後端流式寫入 R2。
* Cloudflare Worker 30 秒限制：長任務改為 Durable Object + 外掛伺服器。

## 13. 里程碑

| 里程碑 | 內容                                          | 預估工期  |
| --- | ------------------------------------------- | ----- |
| M1  | 資料模型 & 基礎 API (Auth、Upload、Translate Queue) | 2 週   |
| M2  | 前端 UI + 積分系統 + 簽到                           | 2 週   |
| M3  | Stripe 整合 + 文件下載                            | 1 週   |
| M4  | 行動端優化 + 公測                                  | 1 週   |
| M5  | 正式上線 & 監控                                   | 0.5 週 |

---

# 14. 代碼框架 (Skeleton)

以下僅展示目錄結構與關鍵檔案，核心邏輯以 `TODO` 標記。

```text
root/
├─ frontend/                # Cloudflare Pages
│  ├─ src/
│  │  ├─ pages/
│  │  │  ├─ index.jsx
│  │  │  ├─ upload.jsx
│  │  │  ├─ signin.jsx
│  │  │  └─ dashboard.jsx
│  │  ├─ components/
│  │  │  ├─ FileUploader.jsx
│  │  │  ├─ LanguageSelector.jsx
│  │  │  ├─ StyleSelector.jsx
│  │  │  └─ JobStatus.jsx
│  │  └─ lib/api.js
│  └─ vite.config.js
├─ worker/                  # Cloudflare Workers (TypeScript)
│  ├─ index.ts
│  ├─ routes/
│  │  ├─ auth.ts
│  │  ├─ upload.ts
│  │  ├─ translate.ts
│  │  ├─ job.ts
│  │  └─ purchase.ts
│  ├─ utils/
│  │  ├─ convert.ts
│  │  ├─ storage.ts
│  │  ├─ points.ts
│  │  └─ queue.ts
│  └─ wrangler.toml
└─ README.md
```

### 14.1 Cloudflare Worker 入口 (worker/index.ts)

```ts
import { Hono } from 'hono';
import { authRouter } from './routes/auth';
import { uploadRouter } from './routes/upload';
import { translateRouter } from './routes/translate';
import { jobRouter } from './routes/job';
import { purchaseRouter } from './routes/purchase';

const app = new Hono<{ Bindings: Env }>();

// TODO: 中間件 – 日誌、CORS、JWT 驗證、錯誤處理

app.route('/api', authRouter);
app.route('/api', uploadRouter);
app.route('/api', translateRouter);
app.route('/api', jobRouter);
app.route('/api', purchaseRouter);

export default app;
```

### 14.2 文件上傳路由 (worker/routes/upload.ts)

```ts
import { Hono } from 'hono';
import { randomUUID } from 'crypto';

export const uploadRouter = new Hono();

uploadRouter.post('/upload', async (c) => {
  // TODO: 驗證 JWT & 积分餘額 (可先不扣)
  const body = await c.req.parseBody();
  const file = body['file'] as File;
  if (!file) return c.json({ message: 'file missing' }, 400);

  const id = randomUUID();
  const key = `uploads/${id}/${file.name}`;

  // TODO: 將文件流寫入 R2
  // await env.R2_BUCKET.put(key, file.stream());

  // TODO: 儲存 uploads 資料表紀錄 (KV / D1)

  return c.json({ uploadId: id, key });
});
```

### 14.3 翻譯任務 (worker/routes/translate.ts)

```ts
uploadRouter.post('/translate', async (c) => {
  /*
    Body: {
      uploadId: string,
      targetLang: string,
      style: 'casual' | 'faithful'
    }
  */
  // TODO: 1) 查詢 upload 記錄, 2) 計算 costPoints
  // TODO: 3) 檢查 & 扣除用戶積分 (atomic)
  // TODO: 4) 推送到 DurableObject/Queue => status = pending
  // TODO: 5) 回傳 jobId
});
```

### 14.4 前端 – 文件上傳頁面 (frontend/src/pages/upload.jsx)

```jsx
import { useState } from 'react';
import { FileUploader } from '@/components/FileUploader';
import { LanguageSelector } from '@/components/LanguageSelector';
import { StyleSelector } from '@/components/StyleSelector';
import api from '@/lib/api';

export default function UploadPage() {
  const [file, setFile] = useState(null);
  const [targetLang, setTargetLang] = useState('zh-CN');
  const [style, setStyle] = useState('casual');
  const [jobId, setJobId] = useState(null);

  const handleSubmit = async () => {
    // TODO: 調用 /api/upload → /api/translate → 回傳 jobId
  };

  return (
    <main className="max-w-xl mx-auto p-4">
      <FileUploader onChange={setFile} />
      <LanguageSelector value={targetLang} onChange={setTargetLang} />
      <StyleSelector value={style} onChange={setStyle} />
      <button className="btn btn-primary w-full mt-4" onClick={handleSubmit}>開始翻譯</button>
      {/* TODO: 若 jobId 存在，顯示 JobStatus 元件 */}
    </main>
  );
}
```

### 14.5 README 片段

````md
## 本地開發
1. `wrangler dev` – 啟動 Workers + R2 模擬器
2. `npm run dev` – 啟動前端 (Vite)

## 部署
```bash
wrangler publish       # 部署 API
npm run build && wrangler pages publish dist   # 部署前端
````

```

## 15. 待辦事項匯總 (TODO)
- [ ] 完成 Cloudflare Worker 中間件（JWT、CORS）
- [ ] R2 檔案流式上傳與清理
- [ ] Durable Object Queue + 外部翻譯 CLI 整合
- [ ] 積分扣除與交易原子性 (D1/DO)
- [ ] Stripe Webhook 驗簽
- [ ] 前端 JobStatus 輪詢
- [ ] i18n – 前端介面多語
- [ ] 測試與 CI/CD（Pages → Preview Branch）

---

> 如需調整功能或流程，請在對話中告訴我，我可以即時修改文檔或深入展開某部分。

```
